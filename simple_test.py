#!/usr/bin/env python3
"""
Simple test to check tool registration step by step
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, '.')

print("Step 1: Testing FastMCP import...")
try:
    from fastmcp import FastMCP
    print("✓ FastMCP imported")
except Exception as e:
    print(f"✗ FastMCP import failed: {e}")
    sys.exit(1)

print("Step 2: Testing server creation...")
try:
    server = FastMCP("test_server")
    print("✓ Server created")
    print(f"  Server has get_tools: {hasattr(server, 'get_tools')}")
except Exception as e:
    print(f"✗ Server creation failed: {e}")
    sys.exit(1)

print("Step 3: Testing tool decorator...")
try:
    @server.tool()
    def test_tool(message: str) -> str:
        """A simple test tool"""
        return f"Echo: {message}"
    
    print("✓ Tool decorator works")
except Exception as e:
    print(f"✗ Tool decorator failed: {e}")
    sys.exit(1)

print("Step 4: Checking registered tools...")
try:
    import asyncio
    tools = asyncio.run(server.get_tools())
    print(f"✓ Found {len(tools)} tools:")
    for tool in tools:
        print(f"  - {tool.name}: {tool.description}")
except Exception as e:
    print(f"✗ Getting tools failed: {e}")

print("Step 5: Testing our server import...")
try:
    from src.server import server as our_server
    print("✓ Our server imported")
    print(f"  Server type: {type(our_server)}")
except Exception as e:
    print(f"✗ Our server import failed: {e}")
    sys.exit(1)

print("Step 6: Testing api_catalog import...")
try:
    from src import api_catalog
    print("✓ API catalog imported")
except Exception as e:
    print(f"✗ API catalog import failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("Step 7: Checking our server tools...")
try:
    import asyncio
    tools = asyncio.run(our_server.get_tools())
    print(f"✓ Found {len(tools)} tools in our server:")
    for tool in tools:
        print(f"  - {tool.name}: {tool.description[:100] if tool.description else 'No description'}...")
except Exception as e:
    print(f"✗ Getting our server tools failed: {e}")
    import traceback
    traceback.print_exc()

print("=== Test Complete ===")
