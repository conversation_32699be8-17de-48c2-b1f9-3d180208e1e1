FROM python:3.10-slim

WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Install the package in development mode
RUN pip install -e .

# Create an entrypoint script
RUN echo '#!/bin/bash\necho "Starting MCP server..."\ncd /app && python -m src streamable-http' > /app/entrypoint.sh \
    && chmod +x /app/entrypoint.sh

# Expose port for the MCP server
EXPOSE 3001

# Run the MCP server in streamable-http mode
CMD ["/app/entrypoint.sh"]
