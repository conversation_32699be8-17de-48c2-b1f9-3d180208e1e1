FROM python:3.10-slim

WORKDIR /app

# Install dependencies
RUN pip install --no-cache-dir pyyaml httpx fastmcp>=2.3.5 mcp>=1.9.0

# Copy project files
COPY . .

# Create an entrypoint script
RUN echo '#!/bin/bash\npython -m pip list\necho "Starting MCP server..."\npython src/__init__.py streamable-http' > /app/entrypoint.sh \
    && chmod +x /app/entrypoint.sh

# Expose port for the MCP server
EXPOSE 3001

# Run the MCP server in streamable-http mode
CMD ["/app/entrypoint.sh"]
