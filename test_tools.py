#!/usr/bin/env python3
"""
Test script to debug tool registration in the MCP server
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, '.')

print("=== MCP Server Tool Registration Test ===")

try:
    print("1. Importing FastMCP...")
    from fastmcp import FastMCP
    print("   ✓ FastMCP imported successfully")
    
    print("2. Creating server instance...")
    from src.server import server
    print("   ✓ Server instance imported")
    
    print("3. Importing api_catalog...")
    from src import api_catalog
    print("   ✓ API catalog imported")
    
    print("4. Checking server tools...")
    print(f"   Server type: {type(server)}")
    print(f"   Server methods: {[m for m in dir(server) if not m.startswith('_')]}")

    # Try different ways to access tools
    try:
        if hasattr(server, 'get_tools'):
            tools = server.get_tools()
            print(f"   ✓ Found {len(tools)} tools via get_tools():")
            for i, tool in enumerate(tools, 1):
                print(f"     {i}. {tool.name}")
                print(f"        Description: {tool.description[:100] if tool.description else 'No description'}...")
        elif hasattr(server, '_tools'):
            tools = server._tools
            print(f"   ✓ Found {len(tools)} tools via _tools: {list(tools.keys())}")
        elif hasattr(server, 'tools'):
            tools = server.tools
            print(f"   ✓ Found tools via .tools: {tools}")
        else:
            print("   ✗ No tools attribute found")

    except Exception as e:
        print(f"   ✗ Error accessing tools: {e}")

        # Try alternative methods
        print("   Trying alternative inspection...")
        for attr in ['_tools', 'tools', '_handlers', 'handlers']:
            if hasattr(server, attr):
                val = getattr(server, attr)
                print(f"   Server.{attr}: {type(val)} = {val}")
            
    print("5. Testing tool decorators...")
    # Check if the decorators are working
    print(f"   get_api_list function: {hasattr(api_catalog, 'get_api_list')}")
    print(f"   get_api_info function: {hasattr(api_catalog, 'get_api_info')}")
    
    if hasattr(api_catalog, 'get_api_list'):
        func = getattr(api_catalog, 'get_api_list')
        print(f"   get_api_list is callable: {callable(func)}")
        print(f"   get_api_list attributes: {dir(func)}")
        
except Exception as e:
    print(f"✗ Error during testing: {e}")
    import traceback
    traceback.print_exc()

print("=== Test Complete ===")
