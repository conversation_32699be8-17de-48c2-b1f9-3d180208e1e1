import os
import sys
import yaml
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Import server modules
from server import server

# Import API endpoints
import api_catalog
# Removed import of dummy module as it no longer contains any endpoints


def load_config():
    try:
        with open(
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.yml"), "r"
        ) as f:
            return yaml.safe_load(f)
    except Exception:
        return {}


if __name__ == "__main__":
    config = load_config()
    server_config = config.get("server", {})
    port = int(os.environ.get("PORT", server_config.get("port", 3001)))
    host = server_config.get("host", "127.0.0.1")
    log_level = server_config.get("log_level", "DEBUG")

    transport_type = sys.argv[1] if len(sys.argv) > 1 else None
    server.settings.log_level = log_level
    if transport_type == "sse":
        server.settings.port = port
        server.settings.host = host
        server.run(transport="sse")
    elif transport_type == "stdio":
        server.run(transport="stdio")
    elif transport_type in ("http", "streamable-http"):
        server.settings.port = port
        server.settings.host = host
        server.run(transport="streamable-http")
    else:
        print("Invalid transport type. Use 'sse', 'stdio', or 'http'.")
        sys.exit(1)