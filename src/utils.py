from typing import Optional, <PERSON><PERSON>
from fastmcp import Context

def extract_token_from_context(ctx: Context) -> <PERSON><PERSON>[Optional[str], dict]:
    """Extract authorization token from the context and return headers.
    
    Args:
        ctx: The FastMCP Context object
        
    Returns:
        Tuple containing:
            - The extracted token (or None if not found)
            - Dictionary of all headers (empty if headers not accessible)
    """
    token = None
    headers = {}
    
    print("\n--- DEBUG: extract_token_from_context called ---")
    print(f"Context type: {type(ctx)}")
    
    if not hasattr(ctx, "get_http_request") or not callable(ctx.get_http_request):
        print("DEBUG: Context does not have get_http_request method")
        return None, {}
        
    try:
        http_request = ctx.get_http_request()
        print(f"DEBUG: HTTP Request type: {type(http_request)}")
        
        if not http_request or not hasattr(http_request, "headers"):
            print("DEBUG: HTTP Request does not have headers attribute")
            return None, {}
            
        # Get all headers
        headers = dict(http_request.headers.items())
        print(f"DEBUG: Headers received: {headers}")
        
        # Extract token from Authorization header
        auth_header = headers.get("authorization")
        print(f"DEBUG: Authorization header: {auth_header}")
        
        if auth_header and auth_header.lower().startswith("bearer "):
            token = auth_header[7:]
            print(f"DEBUG: Extracted token: {token}")
        else:
            print("DEBUG: No valid Bearer token found in Authorization header")
    except Exception as e:
        print(f"Error extracting token: {e}")
        
    return token, headers
