import httpx
import logging
import os
import yaml
from fastmcp import Context

# Handle both module and direct execution imports
try:
    # When running as module (python -m src)
    from .server import server
    from .utils import extract_token_from_context
except ImportError:
    # When running directly (python src/api_catalog.py)
    from server import server
    from utils import extract_token_from_context

# Set default timeout for all HTTP requests
DEFAULT_TIMEOUT = 30.0  # 30 seconds

# Set up logging
logger = logging.getLogger(__name__)

# Load configuration
def load_config():
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.yml")
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                return yaml.safe_load(f)
        return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

# Get base URL from config
config = load_config()
API_BASE_URL = config.get("api", {}).get("base_url", "https://in-cloud.appsentinels.ai/api/web")

@server.tool()
async def get_api_list(tid: str, ctx: Context, pagination=None, filters=None, columns=None, request_body=None):
    """Retrieve a list of API endpoints with filtering, sorting, and pagination capabilities.
    
    This function provides an abstract interface to search and retrieve API endpoints with various
    criteria such as risk level, HTTP method, API direction, and more. It supports pagination, sorting,
    and column selection to customize the returned data.
    
    Args:
        tid (str): Tenant ID for the API request (e.g., 'zeeproduction_default')
        ctx (Context): FastMCP context object containing request information and authentication
        pagination (dict, optional): Controls how results are paginated and sorted. Defaults to None.
            {
                'application': str,  # Target application name (default: tid value)
                'page': int,        # Page number for pagination (default: 0)
                'limit': int,       # Results per page (default: 10)
                'duration': str,    # Time window ('day'/'week') (default: 'day')
                'sort_field': str,  # Field to sort by (default: 'first_discovered')
                'sort_order': str,  # Sort direction ('asc'/'desc') (default: 'desc')
                'show_options': bool # Include OPTIONS method (default: False)
            }
        filters (dict, optional): Criteria to filter API endpoints. Defaults to None.
            {
                'sensitive_data': bool,    # Has sensitive data (True/False)
                'shadow_api': bool,       # Is shadow API (True/False)
                'privileged': bool,       # Requires privileges (True/False)
                'direction': str,         # API direction ('Public'/'Internal')
                'has_session': bool,      # Has session (True/False)
                'is_new': bool,          # Is newly discovered (True/False)
                'is_unused': bool,       # Is unused (True/False)
                'non_conforming': bool,   # Doesn't conform to standards (True/False)
                'tags': list,            # List of API tags to filter by
                'risk_levels': list,     # Risk levels ['Critical', 'High', 'Medium', 'Low']
                'http_methods': list,    # HTTP methods ['GET', 'POST', 'PUT', etc.]
                'uri_pattern': str       # URI pattern to match
            }
        columns (list, optional): List of specific data columns to include in the response. Defaults to None.
        request_body (dict, optional): For backward compatibility - raw request body with filter parameters. Defaults to None.
            
    Note:
        This function provides a more abstract interface over the underlying REST API.
        It handles the conversion between the simplified parameter structure and the
        actual API request format.
        
    Returns:
        dict: List of API endpoints and metadata
            Success: {
                'data': {
                    'total': int,           # Total number of matching APIs
                    'pagination': int,      # Current page number
                    'show_option': bool,    # Whether OPTIONS methods are included
                    'unused_days': int,     # Days threshold for unused APIs
                    'api': [                # List of API endpoints
                        {
                            'apiid': str,         # Unique API identifier
                            'method': str,        # HTTP method (GET, POST, etc.)
                            'uri': str,           # API URI path
                            'endpoint': str,      # Full endpoint URL
                            'direction': str,     # API direction (Public/Internal)
                            'risk_score': str,    # Risk level (Critical/High/Medium/Low)
                            'auth': str,          # Authentication required (Yes/No)
                            'first_discovered': str, # First discovery timestamp
                            'last_observed': str,  # Last observation timestamp
                            ...
                        },
                        ...
                    ]
                }
            }
            Error: {'error': str}
    
    Example:
        >>> pagination = {'application': 'myapp', 'limit': 5}
        >>> filters = {'http_methods': ['GET'], 'sensitive_data': False}
        >>> columns = ['characteristics', 'usage_statistics']
        >>> result = get_api_list('zeeproduction_default', ctx, pagination, filters, columns)
        >>> print(len(result['data']['api']))
        5
    """
    # Extract auth token from the context
    auth_token, headers_dict = extract_token_from_context(ctx)
    
    # Validate tid parameter
    if not isinstance(tid, str):
        logger.warning(f"tid is not a string: {tid}, converting to string")
        tid = str(tid)
    
    # For backward compatibility - handle old parameter names
    query_params = pagination if pagination is not None else {}
    
    logger.info(f"Received get_api_list request: tid={tid}, pagination={pagination}, filters={filters}, columns={columns}, request_body={request_body}")
    url = f"{API_BASE_URL}/{tid}/api-list"
    
    # Ensure pagination is a dictionary
    if not isinstance(query_params, dict):
        try:
            query_params = dict(query_params)
        except (TypeError, ValueError):
            logger.error(f"Could not convert pagination to dict: {pagination}")
            query_params = {}
    
    # Map abstract pagination parameters to API-specific parameters
    params = {
        "application": query_params.get("application", tid),
        "page": query_params.get("page", 0),
        "limit": query_params.get("limit", 10),
        "duration": query_params.get("duration", "day"),
        "show_option": query_params.get("show_options", query_params.get("show_option", False)),
        "sort": query_params.get("sort_field", query_params.get("sort", "first_discovered")),
        "sort_by": query_params.get("sort_order", query_params.get("sort_by", "desc")),
        "precise_match": query_params.get("precise_match", True)
    }
    
    # Initialize request_data for filter parameters
    request_data = {}
    
    # Process abstract filters and map them to API-specific filter parameters
    if isinstance(filters, dict):
        # Map boolean filters to Yes/No strings in lists
        if 'sensitive_data' in filters:
            request_data['sensitive'] = ["Yes" if filters['sensitive_data'] else "No"]
            
        if 'shadow_api' in filters:
            request_data['shadow'] = ["Yes" if filters['shadow_api'] else "No"]
            
        if 'privileged' in filters:
            request_data['privilege'] = ["Yes" if filters['privileged'] else "No"]
            
        if 'direction' in filters:
            request_data['direction'] = [filters['direction']]
            
        if 'has_session' in filters:
            request_data['session'] = ["Yes" if filters['has_session'] else "No"]
            
        if 'is_new' in filters:
            request_data['new'] = "Yes" if filters['is_new'] else "No"
            
        if 'is_unused' in filters:
            request_data['unused'] = "Yes" if filters['is_unused'] else "No"
            
        if 'non_conforming' in filters:
            request_data['non_conforming_api'] = "Yes" if filters['non_conforming'] else "No"
            
        if 'tags' in filters:
            request_data['apitags'] = filters['tags']
            
        if 'risk_levels' in filters:
            request_data['riskscore'] = filters['risk_levels']
            
        if 'http_methods' in filters:
            request_data['method'] = filters['http_methods']
            
        if 'uri_pattern' in filters:
            request_data['uri'] = filters['uri_pattern']
    
    # Handle columns parameter
    if columns is not None and isinstance(columns, list):
        request_data['column_filter'] = columns
    
    # Handle the request_body parameter for backward compatibility
    if request_body is None:
        # Try to extract request body from context
        try:
            if hasattr(ctx, 'request') and hasattr(ctx.request, 'json'):
                request_body = await ctx.request.json()
                logger.debug(f"Extracted request body from context: {request_body}")
            else:
                request_body = {}
        except Exception as e:
            logger.warning(f"Could not extract request body from context: {e}")
            request_body = {}
    
    # Add request_body parameters to request_data for backward compatibility
    if isinstance(request_body, dict):
        for key, value in request_body.items():
            # Only add if not already set by the abstract parameters
            if key not in request_data:
                request_data[key] = value
    
    # Check if we have a valid auth token
    if not auth_token:
        logger.error("No authorization token found in request")
        return {"error": "Authorization token is required"}
        
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json"
    }
        
    try:
        # Add pagination and sorting parameters to the URL
        query_url = url
        query_parts = []
        url_params = [
            'application', 'page', 'limit', 'duration', 'show_option', 
            'sort', 'sort_by', 'precise_match'
        ]
        
        for key, value in params.items():
            # Only add URL parameters to the query string
            if key in url_params and value is not None:
                # Convert boolean values to lowercase strings as expected by the API
                if isinstance(value, bool):
                    value = str(value).lower()
                query_parts.append(f"{key}={value}")
        
        if query_parts:
            query_url = f"{url}?{'&'.join(query_parts)}"
        
        # List of fields that should be in the request body
        filter_fields = [
            'sensitive', 'shadow', 'privilege', 'direction', 'session',
            'new', 'unused', 'non_conforming_api', 'apitags', 'riskscore',
            'method', 'column_filter', 'auth', 'uri'
        ]
        
        # For backward compatibility, check params for filter fields
        # that might have been added from legacy query_params or filters
        for field in filter_fields:
            # Only add if not already in request_data
            if field not in request_data and field in params:
                # Convert string values to lists for fields that expect lists
                if field in ['sensitive', 'shadow', 'privilege', 'direction', 'session', 'apitags', 'riskscore', 'method']:
                    if not isinstance(params[field], list):
                        request_data[field] = [params[field]]
                    else:
                        request_data[field] = params[field]
                else:
                    request_data[field] = params[field]
        
        # Ensure we always have at least some filter parameters in the request body
        # The API requires at least an empty structure for these fields
        if 'sensitive' not in request_data:
            request_data['sensitive'] = []
        if 'column_filter' not in request_data:
            request_data['column_filter'] = []
        
        # Log the final request details for debugging
        logger.debug(f"Making request to {query_url} with body {request_data}")
        
        # Create a client with longer timeout
        async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) as client:
            # Use POST with the exact JSON body format expected by the API
            response = await client.post(query_url, headers=headers, json=request_data, timeout=DEFAULT_TIMEOUT)
            
            # Log the response for debugging
            try:
                response_content = response.text
                logger.debug(f"Response status: {response.status_code}, content: {response_content[:500]}")
            except Exception as e:
                logger.warning(f"Could not log response content: {e}")
            
            # Check for errors
            try:
                response.raise_for_status()
                result = response.json()
                logger.debug(f"Got API list response: {result}")
                return result
            except httpx.HTTPStatusError as exc:
                # Try to extract more detailed error information
                error_detail = "Unknown error"
                try:
                    error_detail = response.json()
                except Exception:
                    try:
                        error_detail = response.text
                    except Exception:
                        pass
                
                logger.error(f"HTTP error occurred: {exc}, details: {error_detail}")
                return {"error": f"HTTP error occurred: {exc}, details: {error_detail}"}
    except httpx.TimeoutException as exc:
        logger.error(f"Request timed out: {exc}", exc_info=True)
        return {"error": "Request timed out"}
    except httpx.HTTPError as exc:
        logger.error(f"HTTP error occurred: {exc}", exc_info=True)
        return {"error": f"HTTP error occurred: {exc}"}
    except Exception as exc:
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        return {"error": f"Unexpected error: {exc}"}


@server.tool()
async def get_api_info(tid: str, apiid: str, ctx: Context) -> dict:
    """Retrieve detailed information about a specific API endpoint.
    
    Args:
        tid (str): Tenant ID for the API request (e.g., 'zeeproduction_default')
        apiid (str): Unique identifier of the API endpoint to retrieve details for
        ctx (Context): FastMCP context object containing request information
    
    Returns:
        dict: API endpoint details containing metadata and security information
            Success: {
                'id': str,
                'method': str,
                'uri': str,
                'riskscore': int,
                ...
            }
            Error: {'error': str}
    
    Example:
        >>> result = get_api_info('zeeproduction_default', 'api123')
        >>> print(result['method'])
        'GET'
    """
    # Extract auth token from the context
    auth_token, headers_dict = extract_token_from_context(ctx)
    
    # Check if we have a valid auth token
    if not auth_token:
        logger.error("No authorization token found in request")
        return {"error": "Authorization token is required"}
    
    # Validate parameters
    if not isinstance(tid, str):
        logger.warning(f"tid is not a string: {tid}, converting to string")
        tid = str(tid)
        
    if not isinstance(apiid, str):
        logger.warning(f"apiid is not a string: {apiid}, converting to string")
        apiid = str(apiid)
    
    logger.debug(f"Received get_api_info request: tid={tid}, apiid={apiid}")
    url = f"{API_BASE_URL}/{tid}/api-info"
    
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json"
    }
    
    params = {"apiid": apiid}
    
    try:
        logger.debug(f"Making request to {url} with params {params}")
        async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) as client:
            response = await client.get(url, headers=headers, params=params, timeout=DEFAULT_TIMEOUT)
            response.raise_for_status()
            result = response.json()
            logger.debug(f"Got API info response: {result}")
            return result
    except httpx.TimeoutException as exc:
        logger.error(f"Request timed out: {exc}", exc_info=True)
        return {"error": "Request timed out"}
    except httpx.HTTPError as exc:
        logger.error(f"HTTP error occurred: {exc}", exc_info=True)
        return {"error": f"HTTP error occurred: {exc}"}
    except Exception as exc:
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        return {"error": f"Unexpected error: {exc}"}
