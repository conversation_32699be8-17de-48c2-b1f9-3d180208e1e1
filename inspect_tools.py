#!/usr/bin/env python3
"""
Inspect the actual tool objects
"""

import sys
import os
import asyncio

# Add the current directory to Python path
sys.path.insert(0, '.')

print("=== Inspecting Tool Objects ===")

from src.__main__ import server
from src import api_catalog

print("1. Getting tools...")
try:
    tools = asyncio.run(server.get_tools())
    print(f"   Found {len(tools)} tools")
    
    print("2. Inspecting each tool...")
    for i, tool in enumerate(tools):
        print(f"   Tool {i+1}:")
        print(f"     Type: {type(tool)}")
        print(f"     Value: {repr(tool)}")
        print(f"     Dir: {dir(tool)[:10]}...")  # First 10 attributes
        
        if hasattr(tool, 'name'):
            print(f"     Name: {tool.name}")
        if hasattr(tool, 'description'):
            print(f"     Description: {tool.description}")
        print()
        
except Exception as e:
    print(f"   Error: {e}")
    import traceback
    traceback.print_exc()

print("3. Trying alternative tool access methods...")
try:
    # Check if there are other ways to access tools
    if hasattr(server, '_tools'):
        print(f"   server._tools: {server._tools}")
    if hasattr(server, 'tools'):
        print(f"   server.tools: {server.tools}")
        
    # Try to access the tool registry directly
    print(f"   Server attributes: {[attr for attr in dir(server) if 'tool' in attr.lower()]}")
    
except Exception as e:
    print(f"   Error: {e}")

print("=== Inspection Complete ===")
