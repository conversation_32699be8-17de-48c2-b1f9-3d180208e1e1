#!/usr/bin/env python3
"""
Debug import issues
"""

import sys
import os
import asyncio

# Add the current directory to Python path
sys.path.insert(0, '.')

print("=== Testing Import Scenarios ===")

print("\n1. Testing direct imports (as if running python src/api_catalog.py)...")
try:
    # Simulate running from src directory
    sys.path.insert(0, './src')
    
    print("  Importing server...")
    from server import server as direct_server
    print(f"  Direct server type: {type(direct_server)}")
    
    print("  Importing api_catalog...")
    import api_catalog
    print("  API catalog imported successfully")
    
    print("  Checking tools...")
    tools = asyncio.run(direct_server.get_tools())
    print(f"  Direct import: {len(tools)} tools found")
    for tool in tools:
        print(f"    - {tool.name}")
        
    # Clean up
    sys.path.remove('./src')
    
except Exception as e:
    print(f"  ✗ Direct import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n2. Testing module imports (as if running python -m src)...")
try:
    # Clear any cached modules
    modules_to_remove = [m for m in sys.modules.keys() if m.startswith('src.') or m in ['server', 'api_catalog', 'utils']]
    for mod in modules_to_remove:
        if mod in sys.modules:
            del sys.modules[mod]
    
    print("  Importing src.server...")
    from src.server import server as module_server
    print(f"  Module server type: {type(module_server)}")
    
    print("  Importing src.api_catalog...")
    from src import api_catalog as module_api_catalog
    print("  Module API catalog imported successfully")
    
    print("  Checking tools...")
    tools = asyncio.run(module_server.get_tools())
    print(f"  Module import: {len(tools)} tools found")
    for tool in tools:
        print(f"    - {tool.name}")
        
except Exception as e:
    print(f"  ✗ Module import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n3. Testing if decorators are being executed...")
try:
    # Check if the functions have the right attributes
    from src import api_catalog
    
    print(f"  get_api_list exists: {hasattr(api_catalog, 'get_api_list')}")
    print(f"  get_api_info exists: {hasattr(api_catalog, 'get_api_info')}")
    
    if hasattr(api_catalog, 'get_api_list'):
        func = api_catalog.get_api_list
        print(f"  get_api_list callable: {callable(func)}")
        print(f"  get_api_list has __wrapped__: {hasattr(func, '__wrapped__')}")
        print(f"  get_api_list type: {type(func)}")
        
except Exception as e:
    print(f"  ✗ Decorator check failed: {e}")
    import traceback
    traceback.print_exc()

print("\n=== Debug Complete ===")
