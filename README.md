# AppSentinels MCP Server

This MCP Server provides integration with AppSentinels API security platform, allowing AI agents to access API security information and insights.

## Features
- **API Catalog**: Access information about API endpoints, including risk levels, security characteristics, and metadata.
- **API Details**: Retrieve detailed information about specific API endpoints.
- **Agent Builder Integration**: Connect and debug the MCP server using the AI Toolkit Agent Builder.
- **MCP Inspector Support**: Debug and inspect the server using [MCP Inspector](https://github.com/modelcontextprotocol/inspector).

## Get started with the AppSentinels MCP Server

## Prerequisites

To run or develop this MCP Server, you will need:
- [Python 3.10+](https://www.python.org/)
- (*Optional*) [uv](https://github.com/astral-sh/uv) for fast virtual environment management
- [Python Debugger Extension](https://marketplace.visualstudio.com/items?itemName=ms-python.debugpy) for debugging in VS Code

## Prepare environment

You can set up the environment using either `uv` or `pip`:

> **Note:** After creating the virtual environment, reload VS Code or your terminal to ensure the correct Python interpreter is used.

| Approach   | Steps |
|------------|-------|
| Using `uv` | 1. Create virtual environment: `uv venv`<br>2. In VS Code, run "Python: Select Interpreter" and choose the new venv<br>3. Install dependencies: `uv pip install -r pyproject.toml --extra dev` |
| Using `pip`| 1. Create virtual environment: `python -m venv .venv`<br>2. In VS Code, run "Python: Select Interpreter" and choose the new venv<br>3. Install dependencies: `pip install -e .[dev]` |


## Project Structure

| Folder / File | Description |
|---------------|-------------|
| `.vscode`     | VS Code debug and task configs |
| `.aitk`       | AI Toolkit configuration |
| `src`         | Source code for the AppSentinels MCP server |


## How to Run, Debug, and Develop

### Run (Streamable HTTP, recommended for web services)

```sh
python src/__init__.py http
```
or
```sh
python src/__init__.py streamable-http
```
The server will start on port 3001 by default. Set the `PORT` environment variable to change it.

### Debug (Agent Builder, recommended for development)
1. Open the VS Code Debug panel.
2. Select `Debug in Agent Builder` and press `F5` to start the server in debug mode (SSE transport).
3. Use the AI Toolkit Agent Builder to test the server with this prompt:
   [Open prompt in Agent Builder](vscode://ms-windows-ai-studio.windows-ai-studio/open_prompt_builder?model_id=github/gpt-4o-mini&system_prompt=You%20are%20an%20API%20security%20expert%20that%20can%20analyze%20API%20endpoints%20and%20provide%20security%20insights&user_prompt=Show%20me%20the%20high-risk%20API%20endpoints%20in%20our%20system&track_from=vsc_md&mcp=appsentinels_mcp)
4. Click `Run` in Agent Builder to test the server.

### Debug (MCP Inspector)
1. Install [Node.js](https://nodejs.org/)
2. Set up Inspector: `cd inspector && npm install`
3. Open the VS Code Debug panel. Select `Debug SSE in Inspector (Edge)` or `Debug SSE in Inspector (Chrome)`. Press F5.
4. When MCP Inspector launches in the browser, click `Connect` to connect this MCP server.
5. Use Inspector to list tools, input parameters, and run/debug your server code.


## Using with Claude Desktop

To use this MCP server with Claude Desktop:

1. Install the `mcp-proxy` npm package globally:
   ```sh
   npm install -g mcp-proxy
   ```

2. Start the AppSentinels MCP server in SSE mode:
   ```sh
   python src/__init__.py sse
   ```

3. Add the following configuration to your Claude Desktop settings:
   ```json
   "appsentinels-server": {
     "command": "mcp-proxy",  
     "args": [
       "http://localhost:3001/sse"
     ]
   }
   ```

4. Restart Claude Desktop and select "appsentinels-server" from the MCP dropdown menu.

## Using with Docker

The AppSentinels MCP server can be run in a Docker container for easier deployment and isolation.

### Building and Running with Docker

```sh
# Build the Docker image
docker build -t appsentinels-mcp .

# Run the container
docker run -p 3001:3001 -v $(pwd)/config.yml:/app/config.yml appsentinels-mcp
```

### Using Docker Compose

```sh
# Start the service
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the service
docker-compose down
```

## Ports and Customization

| Mode           | Default Ports                | Change Port(s) In... |
|----------------|-----------------------------|----------------------|
| HTTP/SSE Server| 3001                        | `PORT` env var, `.vscode/tasks.json`, `src/__init__.py`, `.aitk/mcp.json` |
| MCP Inspector  | 5173 (client), 3000 (server)| `.vscode/tasks.json`, Inspector config |


## Feedback
If you have feedback or suggestions, please open an issue on the [AI Toolkit GitHub repository](https://github.com/microsoft/vscode-ai-toolkit/issues)