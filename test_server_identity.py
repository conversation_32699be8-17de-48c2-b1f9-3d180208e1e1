#!/usr/bin/env python3
"""
Test if both modules use the same server instance
"""

import sys
import os
import asyncio

# Add the current directory to Python path
sys.path.insert(0, '.')

print("=== Testing Server Instance Identity ===")

print("1. Importing server from __main__.py...")
from src.__main__ import server as main_server
print(f"   Main server ID: {id(main_server)}")
print(f"   Main server type: {type(main_server)}")

print("2. Importing server from api_catalog.py...")
# We need to import api_catalog to trigger the server import there
from src import api_catalog
# Now get the server that api_catalog is using
import src.api_catalog
catalog_server = src.api_catalog.server
print(f"   Catalog server ID: {id(catalog_server)}")
print(f"   Catalog server type: {type(catalog_server)}")

print("3. Checking if they're the same instance...")
print(f"   Same instance: {main_server is catalog_server}")

print("4. Checking tools on main server...")
try:
    main_tools = asyncio.run(main_server.get_tools())
    print(f"   Main server tools: {len(main_tools)}")
    for tool in main_tools:
        print(f"     - {tool.name}")
except Exception as e:
    print(f"   Error: {e}")

print("5. Checking tools on catalog server...")
try:
    catalog_tools = asyncio.run(catalog_server.get_tools())
    print(f"   Catalog server tools: {len(catalog_tools)}")
    for tool in catalog_tools:
        print(f"     - {tool.name}")
except Exception as e:
    print(f"   Error: {e}")

print("6. Testing manual tool registration...")
try:
    @main_server.tool()
    def manual_test_tool(msg: str) -> str:
        """Manual test tool"""
        return f"Manual: {msg}"
    
    print("   Manual tool registered")
    
    # Check tools again
    main_tools = asyncio.run(main_server.get_tools())
    print(f"   Main server tools after manual registration: {len(main_tools)}")
    for tool in main_tools:
        print(f"     - {tool.name}")
        
except Exception as e:
    print(f"   Manual registration error: {e}")

print("=== Test Complete ===")
